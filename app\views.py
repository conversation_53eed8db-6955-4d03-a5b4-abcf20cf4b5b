from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.template.loader import get_template
from django.template.exceptions import TemplateDoesNotExist
from django.utils import timezone
import os
import json

# 导入智能模板路由器
from utils.template_router import smart_render


# Create your views here.


def index(request):
    # locals函数可以将该函数中出现过的所有变量传入到展示页面中，即index.html文件中
    return render(request, 'index.html', locals())

# 添加处理页面的视图
def page_view(request, page_name):
    """
    处理动态页面请求，尝试渲染请求的页面，如果不存在则渲染404页面
    """
    # 检查请求URL，确定正确的模板路径
    if request.path.startswith('/pages/'):
        template_path = f'pages/{page_name}'
    else:
        # 如果是根路径下的请求，直接使用页面名称
        template_path = page_name
    
    try:
        # 检查模板文件是否存在
        from django.template.loader import get_template
        from django.template.exceptions import TemplateDoesNotExist
        try:
            get_template(template_path)
            return render(request, template_path, locals())
        except TemplateDoesNotExist:
            # 如果模板不存在，尝试在pages目录下查找
            if not template_path.startswith('pages/'):
                try:
                    get_template(f'pages/{page_name}')
                    return render(request, f'pages/{page_name}', locals())
                except TemplateDoesNotExist:
                    pass
            # 如果都找不到，返回404
            return page_404(request)
    except Exception as e:
        print(f"加载页面 {page_name} 时出错: {str(e)}")
        return page_404(request)

# 专门处理404页面的视图
def page_404(request):
    """
    处理404页面请求
    """
    return render(request, '404.html', locals(), status=404)


# ==================== 新的后台管理框架视图函数 ====================

def admin_framework(request):
    """
    后台管理框架主页面视图
    渲染新的管理框架页面
    """
    try:
        from django.conf import settings

        context = {
            'page_title': '后台管理系统',
            'framework_version': '1.0.0',
            'current_route': 'home',
            'debug': settings.DEBUG,
            'api_base_path': '/admin/backend',
            'framework_page_path': '/admin/backend/framework/page'
        }

        print(f"[DEBUG] 框架页面上下文: {context}")
        return render(request, 'admin-framework.html', context)

    except Exception as e:
        print(f"加载管理框架页面时出错: {str(e)}")
        return HttpResponse(f"框架加载失败: {str(e)}", status=500)


def framework_page_view(request, route_path):
    """
    处理框架子页面的AJAX请求
    根据路由路径返回对应的页面内容

    Args:
        request: Django请求对象
        route_path: 路由路径，如 'dashboard', 'products/list', 'users/groups'

    Returns:
        HttpResponse: 页面HTML内容或错误信息
    """


    # 检查是否为AJAX请求（在开发环境下放宽检查）
    from django.conf import settings
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if not is_ajax and not settings.DEBUG:
        # 生产环境下严格检查AJAX请求
        return JsonResponse({
            'error': '此端点仅支持AJAX请求',
            'code': 'INVALID_REQUEST_TYPE',
            'debug_info': f'路由: {route_path}, 请求类型: {request.method}'
        }, status=400)
    elif not is_ajax and settings.DEBUG:
        # 开发环境下给出警告但允许继续
        print(f"[WARNING] 非AJAX请求访问框架页面: {route_path} (开发环境允许)")

    try:
        # 路由映射表 - 将前端路由映射到实际的模板文件（根据实际文件位置调整）
        route_mapping = {
            # 控制台 - 对应原版 pages/home.html
            'home': 'pages/home.html',

            # 商品列表 - 对应原版 productlist.html（在pages目录下）
            'productlist': 'pages/productlist.html',

            # 分类管理 - 对应原版 category.html（在pages目录下）
            'category': 'pages/category.html',

            # 卡密管理 - 对应原版 cardstock.html（在pages目录下）
            'cardstock': 'pages/cardstock.html',

            # 卡券管理 - 对应原版 coupon.html（在pages目录下）
            'coupon': 'pages/coupon.html',

            # 定价模板 - 对应原版 PriceTemplate.html（在pages目录下）
            'PriceTemplate': 'pages/PriceTemplate.html',

            # 对接中心 - 对应原版 DockingCenter.html（在pages目录下）
            'DockingCenter': 'pages/DockingCenter.html',

            # 订单列表 - 新创建的 orderlist.html（在pages目录下）
            'orderlist': 'pages/orderlist.html',

            # 用户列表 - 新创建的 userlist.html（在根目录下）
            'userlist': 'userlist.html',

            # 会员等级 - 对应原版 usergroup.html（在pages目录下）
            'usergroup': 'pages/usergroup.html',

            # 支付设置 - 对应原版 payment.html（在pages目录下）
            'payment': 'pages/payment.html',

            # 插件市场 - 对应原版 plugin.html（在pages目录下）
            'plugin': 'pages/plugin.html',

            # 404错误页面 - 对应 pages/404.html
            '404': 'pages/404.html',
        }

        # 获取对应的模板路径
        template_path = route_mapping.get(route_path)
        print(f"[DEBUG] 路由映射结果: {route_path} -> {template_path}")

        if not template_path:
            # 如果路由不存在，返回404状态码让前端处理
            print(f"[ERROR] 路由不存在: {route_path}")
            print(f"[DEBUG] 可用路由: {list(route_mapping.keys())}")

            error_html = f'''
            <div class="af-error-page" style="padding: 40px; text-align: center; color: #666; font-family: Arial, sans-serif;">
                <h2 style="color: #e74c3c; margin-bottom: 20px;">页面未找到</h2>
                <p style="font-size: 16px; margin-bottom: 10px;">路由: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">{route_path}</code></p>
                <p style="color: #999; margin-bottom: 30px;">请检查路由配置或联系管理员</p>
                <div style="margin-top: 20px;">
                    <strong>可用路由:</strong>
                    <div style="margin-top: 10px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto;">
                        {', '.join(route_mapping.keys())}
                    </div>
                </div>
            </div>
            '''
            return HttpResponse(error_html, status=404)

        # 尝试渲染模板
        try:
            print(f"[DEBUG] 尝试加载模板: {template_path}")

            # 检查模板是否存在
            get_template(template_path)
            print(f"[DEBUG] 模板加载成功: {template_path}")

            context = {
                'current_route': route_path,
                'page_title': _get_page_title(route_path),
                'is_framework_page': True,
                'framework_mode': True
            }

            # 渲染并返回页面内容
            return render(request, template_path, context)

        except TemplateDoesNotExist as e:
            print(f"[ERROR] 模板不存在: {template_path}")
            print(f"[ERROR] 模板查找错误详情: {str(e)}")

            # 返回详细的错误信息
            error_html = f'''
            <div class="af-error-page" style="padding: 40px; text-align: center; color: #666; font-family: Arial, sans-serif;">
                <h2 style="color: #e74c3c; margin-bottom: 20px;">模板未找到</h2>
                <p style="font-size: 16px; margin-bottom: 10px;">路由: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">{route_path}</code></p>
                <p style="font-size: 16px; margin-bottom: 10px;">模板路径: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">{template_path}</code></p>
                <p style="color: #999; margin-bottom: 20px;">模板文件不存在或无法访问</p>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: left;">
                    <strong>错误详情:</strong><br>
                    <code style="color: #e74c3c;">{str(e)}</code>
                </div>
                <button onclick="history.back()"
                        style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    返回上页
                </button>
                <button onclick="window.location.href='/admin/backend/framework/'"
                        style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    返回首页
                </button>
            </div>
            '''
            return HttpResponse(error_html, status=404)

    except Exception as e:
        print(f"处理框架页面请求时出错 - 路由: {route_path}, 错误: {str(e)}")

        # 返回错误页面
        return HttpResponse(
            f'''
            <div class="af-error-page" style="padding: 40px; text-align: center; color: #666;">
                <h2 style="color: #e74c3c;">页面加载失败</h2>
                <p>路由: <code>{route_path}</code></p>
                <p>错误信息: {str(e)}</p>
                <button onclick="AdminFramework.navigateTo('home')"
                        style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    返回控制台
                </button>
            </div>
            ''',
            status=500
        )


def _get_page_title(route_path):
    """
    根据路由路径获取页面标题（与原版index.html完全一致）

    Args:
        route_path: 路由路径

    Returns:
        str: 页面标题
    """
    title_mapping = {
        'home': '控制台',
        'productlist': '商品列表',
        'category': '分类管理',
        'cardstock': '卡密管理',
        'coupon': '卡券管理',
        'PriceTemplate': '定价模板',
        'DockingCenter': '对接中心',
        'orderlist': '订单列表',
        'userlist': '用户列表',
        'usergroup': '会员等级',
        'payment': '支付设置',
        'plugin': '插件市场',
        '404': '页面未找到',
    }

    return title_mapping.get(route_path, '后台管理')


# ==================== Vue混合渲染测试视图 ====================

def vue_index_page(request):
    """
    原始index.vue页面测试
    """
    try:
        return smart_render(request, 'pages/index.vue')
    except Exception as e:
        print(f"Vue index页面加载失败: {str(e)}")
        return HttpResponse(f"Vue index页面加载失败: {str(e)}", status=500)


def smart_page_view(request, page_name):
    """
    智能页面视图 - 支持HTML和Vue模板的混合渲染
    这是对原有page_view函数的增强版本
    """
    # 检查请求URL，确定正确的模板路径
    if request.path.startswith('/pages/'):
        template_path = f'pages/{page_name}'
    else:
        template_path = page_name
    
    try:
        # 准备上下文数据
        context = {
            'page_name': page_name,
            'request_path': request.path,
            'current_time': timezone.now(),
        }
        
        # 首先尝试Vue模板
        vue_template_path = f'vue/{template_path}.vue'
        try:
            return smart_render(request, vue_template_path, context)
        except TemplateDoesNotExist:
            pass
        
        # 然后尝试HTML模板
        html_template_path = f'{template_path}.html'
        try:
            return smart_render(request, html_template_path, context)
        except TemplateDoesNotExist:
            pass
        
        # 如果都找不到，尝试在pages目录下查找
        if not template_path.startswith('pages/'):
            try:
                return smart_render(request, f'pages/{page_name}.html', context)
            except TemplateDoesNotExist:
                pass
        
        # 如果都找不到，返回404
        return page_404(request)
        
    except Exception as e:
        print(f"智能页面加载失败 {page_name}: {str(e)}")
        return page_404(request)

